{"/promo/skywind": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion:skywind", "keyentity:promotion:skywind:create"]}], "tags": ["Promo"], "summary": "Creates promotion", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PromotionWithoutIds"}}], "responses": {"201": {"description": "Created promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 684: Referenced item is not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion:skywind", "keyentity:promotion:skywind:edit"]}], "tags": ["Promo"], "summary": "Updates promotion", "parameters": [{"name": "info", "in": "body", "schema": {"$ref": "#/definitions/PromotionUpdateData"}}], "responses": {"200": {"description": "Updated promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 219: Can not update archived promo\n"}, "404": {"description": "- 680: Promotion not found\n- 684: Referenced item is not found\n"}}}}, "/entities/{path}/promo/skywind": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["promotion:skywind", "promotion:skywind:create"]}], "tags": ["Promo"], "summary": "Creates promotion by path", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PromotionWithoutIds"}}], "responses": {"201": {"description": "Created promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 684: Referenced item is not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["promotion:skywind", "promotion:skywind:edit"]}], "tags": ["Promo"], "summary": "Updates promotion by path", "parameters": [{"name": "info", "in": "body", "schema": {"$ref": "#/definitions/PromotionUpdateData"}}], "responses": {"200": {"description": "Updated promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 219: Can not update archived promo\n"}, "404": {"description": "- 51: Could not find entity\n- 680: Promotion not found\n- 684: Referenced item is not found\n"}}}}, "/promo/started": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["promosId"], "type": "object", "properties": {"promosId": {"type": "array", "description": "List of promo ids to apply status", "items": {"type": "string"}, "example": ["pQ3513OE", "pA3513OZ"]}}}}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:edit"]}], "tags": ["Promo"], "summary": "Starts promotion if today is promo start day. Normally promotion starts automatically", "responses": {"200": {"description": "Promotions updated and returned", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionShortInfo"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 680: Promotion not found\n"}}}}, "/entities/{path}/promo/started": {"parameters": [{"$ref": "#/parameters/path"}, {"in": "body", "name": "info", "required": true, "schema": {"required": ["promosId"], "type": "object", "properties": {"promosId": {"type": "array", "description": "List of promo ids to apply status", "items": {"type": "string"}, "example": ["pQ3513OE", "pA3513OZ"]}}}}], "put": {"security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:edit"]}], "tags": ["Promo"], "summary": "Starts promotion if today is promo start day. Normally promotion starts automatically", "responses": {"200": {"description": "Promotions updated and returned", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionShortInfo"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 680: Promotion not found\n"}}}}, "/promo/{promoId}": {"parameters": [{"$ref": "#/parameters/promoId"}, {"$ref": "#/parameters/includePromoTotals"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:view"]}], "tags": ["Promo"], "summary": "Gets a promotion by id", "responses": {"200": {"description": "Promotion found and returned", "schema": {"$ref": "#/definitions/Promotion"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 680: Promotion not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:delete"]}], "tags": ["Promo"], "summary": "Archives promotion", "responses": {"204": {"description": "Successfully archived promotion"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 680: Promotion not found\n"}}}}, "/promo": {"get": {"parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/promoArchived"}, {"$ref": "#/parameters/promoEnabled"}, {"$ref": "#/parameters/promoStatus"}, {"$ref": "#/parameters/promoState__in"}, {"$ref": "#/parameters/startDate__gte"}, {"$ref": "#/parameters/startDate__lte"}, {"$ref": "#/parameters/endDate__gte"}, {"$ref": "#/parameters/endDate__lte"}, {"$ref": "#/parameters/createdAt__gte"}, {"$ref": "#/parameters/createdAt__lte"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/promoTitle__contains"}, {"$ref": "#/parameters/labelsIdIn"}, {"$ref": "#/parameters/promoType"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/includePromoTotals"}, {"$ref": "#/parameters/includePromoGames"}, {"$ref": "#/parameters/owner"}, {"$ref": "#/parameters/promoExternalId"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:view"]}], "tags": ["Promo"], "summary": "Gets list of promotions", "responses": {"200": {"description": "List of promotions under the key entity tree", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionShortInfo"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:create"]}], "tags": ["Promo"], "summary": "Creates promotion", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PromotionWithoutIds"}}], "responses": {"201": {"description": "Created promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 684: Referenced item is not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:edit"]}], "tags": ["Promo"], "summary": "Updates promotion", "parameters": [{"name": "info", "in": "body", "schema": {"$ref": "#/definitions/PromotionUpdateData"}}], "responses": {"200": {"description": "Updated promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 219: Can not update archived promo\n"}, "404": {"description": "- 680: Promotion not found\n- 684: Referenced item is not found\n"}}}}, "/promo/status": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["promosId", "status"], "type": "object", "properties": {"promosId": {"type": "array", "description": "List of promo ids to apply status", "items": {"type": "string"}, "example": ["pQ3513OE", "pA3513OZ"]}, "status": {"type": "string", "description": "Promo state - active or inactive", "example": "active"}}}}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:edit"]}], "tags": ["Promo"], "summary": "Sets promotion state", "responses": {"200": {"description": "Promotions updated and returned", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionShortInfo"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 680: Promotion not found\n"}}}}, "/entities/{path}/promo/status": {"parameters": [{"$ref": "#/parameters/path"}, {"in": "body", "name": "info", "required": true, "schema": {"required": ["promosId", "status"], "type": "object", "properties": {"promosId": {"type": "array", "description": "List of promo ids to apply status", "items": {"type": "string"}, "example": ["pQ3513OE", "pA3513OZ"]}, "status": {"type": "string", "description": "Promo state - active or inactive", "example": "active"}}}}], "put": {"security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:edit"]}], "tags": ["Promo"], "summary": "Sets promotion state by path", "responses": {"200": {"description": "Promotions updated and returned", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionShortInfo"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 680: Promotion not found\n"}}}}, "/entities/{path}/promo": {"get": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/promoArchived"}, {"$ref": "#/parameters/promoEnabled"}, {"$ref": "#/parameters/promoStatus"}, {"$ref": "#/parameters/promoState__in"}, {"$ref": "#/parameters/startDate__gte"}, {"$ref": "#/parameters/startDate__lte"}, {"$ref": "#/parameters/endDate__gte"}, {"$ref": "#/parameters/endDate__lte"}, {"$ref": "#/parameters/createdAt__gte"}, {"$ref": "#/parameters/createdAt__lte"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/promoTitle__contains"}, {"$ref": "#/parameters/labelsIdIn"}, {"$ref": "#/parameters/promoType"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/includePromoTotals"}, {"$ref": "#/parameters/includePromoGames"}, {"$ref": "#/parameters/owner"}, {"$ref": "#/parameters/promoExternalId"}], "security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:view"]}], "tags": ["Promo"], "summary": "Gets list of promotions by path", "responses": {"200": {"description": "List of promotions under specified brand", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionShortInfo"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:create"]}], "tags": ["Promo"], "summary": "Creates promotion by path", "parameters": [{"$ref": "#/parameters/path"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PromotionWithoutIds"}}], "responses": {"201": {"description": "Created promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 684: Referenced item is not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:edit"]}], "tags": ["Promo"], "summary": "Updates promotion by path", "parameters": [{"$ref": "#/parameters/path"}, {"name": "info", "in": "body", "schema": {"$ref": "#/definitions/PromotionUpdateData"}}], "responses": {"200": {"description": "Updated promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 219: Can not update archived promo\n"}, "404": {"description": "- 51: Could not find entity\n- 680: Promotion not found\n- 684: Referenced item is not found\n"}}}}, "/entities/{path}/promo/{promoId}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/promoId"}, {"$ref": "#/parameters/includePromoTotals"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:view"]}], "tags": ["Promo"], "summary": "Gets a promotion by id and path", "responses": {"200": {"description": "Promotion found and returned", "schema": {"$ref": "#/definitions/Promotion"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n- 680: Promotion not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:delete"]}], "tags": ["Promo"], "summary": "Archives promotion by path", "responses": {"204": {"description": "Successfully archived promotion"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 51: Could not find entity\n- 680: Promotion not found\n"}}}}, "/entities/{path}/promo/{promoId}/players": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/promoId"}], "get": {"parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:view"]}], "tags": ["Promo"], "summary": "Get promotion players by path", "responses": {"200": {"description": "Promotion found and returned", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionPlayer"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 680: Promotion not found\n"}}}, "put": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Add promo players by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 680: Promotion not found\n"}}}, "post": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/AddPromoByPlayersCodesData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Add promo players by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 680: Promotion not found\n"}}}}, "/promo/{promoId}/players": {"parameters": [{"$ref": "#/parameters/promoId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:view"]}], "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}], "tags": ["Promo"], "summary": "Get promotion players", "responses": {"200": {"description": "Promotion found and returned", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionPlayer"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 680: Promotion not found\n"}}}, "put": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Add promo players", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 680: Promotion not found\n"}}}, "post": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/AddPromoByPlayersCodesData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Add promo players", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 680: Promotion not found\n"}}}}, "/promo/{promoId}/owner": {"parameters": [{"$ref": "#/parameters/promoId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion:owner"]}], "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["owner"], "type": "object", "properties": {"owner": {"type": "string", "description": "Promo owner - skywind or operator", "example": "operator"}}}}], "tags": ["Promo"], "summary": "Sets promotion owner", "responses": {"200": {"description": "Promotions updated and returned", "schema": {"$ref": "#/definitions/PromotionShortInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 680: Promotion not found\n"}}}}, "/entities/{path}/promo/{promoId}/owner": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/promoId"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["promotion:owner"]}], "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["owner"], "type": "object", "properties": {"owner": {"type": "string", "description": "Promo owner - skywind or operator", "example": "operator"}}}}], "tags": ["Promo"], "summary": "Sets promotion owner by path", "responses": {"200": {"description": "Promotions updated and returned", "schema": {"$ref": "#/definitions/PromotionShortInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 680: Promotion not found\n"}}}}, "/players/{playerCode}/promo/{promoId}/freeBetLeft": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Get promo info and freeBetLeft for player", "responses": {"200": {"description": "Info about player promo", "schema": {"$ref": "#/definitions/PlayerPromotionWithFreeBetLeft"}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/players/{playerCode}/bonuses/{bonusId}": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/bonusId"}, {"$ref": "#/parameters/playerBonusType"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:bonus", "keyentity:player:bonus:view"]}], "tags": ["Promo"], "summary": "Get player bonus info", "responses": {"200": {"description": "Info about player bonus", "schema": {"$ref": "#/definitions/PlayerBonus"}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:bonus", "keyentity:player:bonus:delete"]}], "tags": ["Promo"], "summary": "Delete player bonus", "responses": {"204": {"description": "No content"}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/players/{playerCode}/bonuses": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/playerBonusType"}], "post": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/CreatePlayerBonusDto"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:bonus", "keyentity:player:bonus:create"]}], "tags": ["Promo"], "summary": "Create player bonus", "responses": {"200": {"description": "Info about player bonus", "schema": {"$ref": "#/definitions/PlayerBonus"}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:bonus", "keyentity:player:bonus:view"]}], "tags": ["Promo"], "summary": "Get all player bonuses", "responses": {"200": {"description": "Info about player bonuses", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerBonus"}}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:bonus", "keyentity:player:bonus:delete"]}], "tags": ["Promo"], "summary": "Remove all player bonuses", "responses": {"204": {"description": "No content"}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/players/{playerCode}/bonuses/{bonusId}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/bonusId"}, {"$ref": "#/parameters/playerBonusType"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["player:bonus", "player:bonus:view"]}], "tags": ["Promo"], "summary": "Get player bonus info", "responses": {"200": {"description": "Info about player bonus", "schema": {"$ref": "#/definitions/PlayerBonus"}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["player:bonus", "player:bonus:delete"]}], "tags": ["Promo"], "summary": "Delete player bonus", "responses": {"204": {"description": "No content"}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/players/{playerCode}/bonuses": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/playerBonusType"}], "post": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/CreatePlayerBonusDto"}}], "security": [{"apiKey": []}, {"Permissions": ["player:bonus", "player:bonus:create"]}], "tags": ["Promo"], "summary": "Create player bonus", "responses": {"200": {"description": "Info about player bonus", "schema": {"$ref": "#/definitions/PlayerBonus"}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["player:bonus", "player:bonus:view"]}], "tags": ["Promo"], "summary": "Get all player bonuses", "responses": {"200": {"description": "Info about player bonuses", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerBonus"}}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:bonus", "keyentity:player:bonus:delete"]}], "tags": ["Promo"], "summary": "Remove all player bonuses", "responses": {"204": {"description": "No content"}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/players/{playerCode}/promo/{promoId}/freeBetLeft": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo"], "summary": "Get promo info and freeBetLeft for player by path", "responses": {"200": {"description": "Info about player promo", "schema": {"$ref": "#/definitions/PlayerPromotionWithFreeBetLeft"}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/players/{playerCode}/promo": {"get": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"name": "currency", "in": "query", "description": "Player currency for merchant entities. For internal players this param will be ignored.", "required": false, "type": "string"}, {"$ref": "#/parameters/promoType"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Gets list of player promotions", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerPromotion"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/players/{playerCode}/freebet/{promoId}": {"put": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Apply freebet promotion to the player", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerFreebetPromotion"}}}, "400": {"description": "- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON><PERSON> not found\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/players/{playerCode}/bonuscoin/{promoId}": {"put": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Apply bonus coin promotion to the player", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerBonusCoinPromotion"}}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/promo/bonuscoin/{promoId}/players/group": {"put": {"parameters": [{"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Applys bonus coin promotion to group of players under the key entity", "description": "Method adds bonus coin promotion to several players according to filtering. If there aren't filters method adds the promotion to all brand's players (so, please think twice before adding)", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 687: Error saving promotion transaction operation\n- 688: Promo already has been added to player\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON><PERSON> not found\n"}}}}, "/players/{playerCode}/bonuscoin/{promoId}/prolong": {"put": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/BonusCoinProlongData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Prolong bonus coin promotion for the player", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerBonusCoinPromotion"}}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/players/{playerCode}/promo/{promoId}": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "put": {"parameters": [{"in": "body", "name": "info", "schema": {"$ref": "#/definitions/BonusCoinProlongData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Add promo rewards for player", "responses": {"200": {"description": "List of current promotions of the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerPromotion"}}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "patch": {"parameters": [{"in": "body", "name": "info", "schema": {"$ref": "#/definitions/BonusCoinProlongData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Update promo rewards for player (only for BNS promotions)", "responses": {"200": {"description": "List of current promotions of the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerPromotion"}}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Get promo rewards for player", "responses": {"200": {"description": "List of current promotions of the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerPromotion"}}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Reset promo rewards for player", "parameters": [{"name": "force", "in": "query", "description": "Force to reset promo for player even if it has already been started", "required": false, "type": "boolean"}], "responses": {"204": {"description": "Promo rewards for player was reset"}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/players/{playerCode}/promo": {"get": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"name": "currency", "in": "query", "description": "Player currency for merchant entities. For internal players this param will be ignored.", "required": false, "type": "string"}, {"$ref": "#/parameters/promoType"}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo"], "summary": "Gets list of player promotions by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerPromotion"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/players/{playerCode}/freebet/{promoId}": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Apply freebet promotion to the player by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerFreebetPromotion"}}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON><PERSON> not found\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/players/{playerCode}/bonuscoin/{promoId}": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Apply bonus coin promotion to the player by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerBonusCoinPromotion"}}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/promo/bonuscoin/{promoId}/players/group": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Applies bonus coin promotion to group of players for a brand by path", "description": "Method adds bonus coin promotion to several players according to filtering. If there aren't filters method adds the promotion to all brand's players (so, please think twice befor adding)", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 687: Error saving promotion transaction operation\n- 688: Promo already has been added to player\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 85: <PERSON><PERSON><PERSON>cy not found\n"}}}}, "/entities/{path}/players/{playerCode}/bonuscoin/{promoId}/prolong": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/BonusCoinProlongData"}}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Prolong bonus coin promotion for the player by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerBonusCoinPromotion"}}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/players/{playerCode}/promo/{promoId}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "put": {"parameters": [{"in": "body", "name": "info", "schema": {"$ref": "#/definitions/BonusCoinProlongData"}}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo"], "summary": "Add promo rewards for player by path", "responses": {"200": {"description": "List of current promotions of the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerPromotion"}}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "patch": {"parameters": [{"in": "body", "name": "info", "schema": {"$ref": "#/definitions/BonusCoinProlongData"}}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Update promo rewards for player by path (only for BNS promotions)", "responses": {"200": {"description": "List of current promotions of the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerPromotion"}}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo"], "summary": "Get promo rewards for player by path", "responses": {"200": {"description": "List of current promotions of the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerPromotion"}}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo"], "summary": "Reset promo rewards for player by path", "parameters": [{"name": "force", "in": "query", "description": "Force to reset promo for player even if it has already been started", "required": false, "type": "boolean"}], "responses": {"204": {"description": "Promo rewards for player was reset"}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/promo/freebet/{promoId}/players/group": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Applys freebet promotion to group of players for a brand by path", "description": "Method adds freebet promotion to several players according to filtering. If there aren't filters method adds the promotion to all brand's players (so, please think twice befor adding)", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 687: Error saving promotion transaction operation\n- 688: Promo already has been added to player\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON><PERSON> not found\n"}}}}, "/promo/freebet/{promoId}/players/group": {"put": {"parameters": [{"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Applys freebet promotion to group of players under the key entity", "description": "Method adds freebet promotion to several players according to filtering. If there aren't filters method adds the promotion to all brand's players (so, please think twice befor adding)", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 687: Error saving promotion transaction operation\n- 688: Promo already has been added to player\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON><PERSON> not found\n"}}}}, "/promolabels": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:view"]}], "tags": ["Promo"], "summary": "Gets list of all labels used under current brand", "description": "Gets list of promotion labels filtered (or not) by group", "responses": {"200": {"description": "List of brand promotion labels", "schema": {"type": "array", "items": {"$ref": "#/definitions/LabelInfo"}}}, "400": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/promo/{promoId}/projection": {"parameters": [{"$ref": "#/parameters/promoId"}, {"in": "query", "name": "currency", "required": false, "description": "Currency to use for calculations ([ISO 4217](http://en.wikipedia.org/wiki/ISO_4217) currency code)", "type": "string"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:view"]}], "tags": ["Promo"], "summary": "Gets a promotion projected and actual info", "responses": {"200": {"description": "Promotion projected and actual info", "schema": {"$ref": "#/definitions/PromoProjectionAndActualInfo"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 680: Promotion not found\n"}}}}, "/promo/{promoId}/eligibleplayers": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view", "keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Finds players under the specified entity that are eligible for given promo", "parameters": [{"$ref": "#/parameters/promoId"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/firstNameStrictEquality"}, {"$ref": "#/parameters/firstNameContains"}, {"$ref": "#/parameters/firstNameNotContains"}, {"$ref": "#/parameters/firstNameIn"}, {"$ref": "#/parameters/lastNameStrictEquality"}, {"$ref": "#/parameters/lastNameContains"}, {"$ref": "#/parameters/lastNameNotContains"}, {"$ref": "#/parameters/lastNameIn"}, {"$ref": "#/parameters/emailStrictEquality"}, {"$ref": "#/parameters/emailContains"}, {"$ref": "#/parameters/emailNotContains"}, {"$ref": "#/parameters/emailIn"}, {"$ref": "#/parameters/gameGroupStrictEquality"}, {"$ref": "#/parameters/gameGroupIn"}, {"$ref": "#/parameters/countryStrictEquality"}, {"$ref": "#/parameters/countryIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/lastLogin"}, {"$ref": "#/parameters/lastLogin__gt"}, {"$ref": "#/parameters/lastLogin__lt"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/status"}], "responses": {"200": {"description": "List of players eligible for promo", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 101: Not a brand\n- 403: Key is not valid for sort by\n- 711: Promo is not in a valid state\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group is not found\n- 680: Promotion not found\n"}}}}}