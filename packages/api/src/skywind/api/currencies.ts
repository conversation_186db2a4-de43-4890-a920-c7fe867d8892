import { Currencies } from "../../../../../../sw-currency-exchange/src/skywind/currencies";
import { Router } from "express";
import { auditable, authenticate, authorize } from "./middleware/middleware";

const router: Router = Router();

router.get("/currencies-store", authenticate, authorize, async (req, res, next) => {
    try {
        const currencies = await Currencies.fetch();
        res.send(currencies);
    } catch (err) {
        next(err);
    }
});

router.get("/currencies-store/:code", authenticate, authorize, async (req, res, next) => {
    try {
        const currencies = await Currencies.fetch();
        if (!currencies[req.params.code]) {
            return res.status(404).send({ error: `Currency with code ${req.params.code} not found` });
        }
        res.send(currencies[req.params.code]);
    } catch (err) {
        next(err);
    }
});

router.post("/currencies-store", authenticate, authorize, auditable, async (req, res, next) => {
    try {
        const currency = await Currencies.create(req.body.code);
        res.status(201).send(currency);
    } catch (err) {
        next(err);
    }
});

router.patch("/currencies-store/:code", authenticate, authorize, auditable, async (req, res, next) => {
    try {
        const currency = await Currencies.update(req.params.code, req.body);
        res.send(currency);
    } catch (err) {
        next(err);
    }
});

router.delete("/currencies-store/:code", authenticate, authorize, auditable, async (req, res, next) => {
    try {
        await Currencies.delete(req.params.code);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
});

export default router;
