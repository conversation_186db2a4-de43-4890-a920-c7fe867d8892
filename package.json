{"name": "@skywind-group/sw-wallet-adapter-core", "version": "0.6.199", "description": "", "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"clean": "gulp clean", "compile": "gulp compile-server", "test": "gulp test", "tslint": "gulp tslint", "version": "gulp version", "status": "gulp status"}, "repository": {"type": "git", "url": "ssh://******************************:7999/swb/sw-wallet-adapter-core.git"}, "author": "", "license": "ISC", "devDependencies": {"@skywind-group/sw-utils": "1.0.10", "@types/cookiejar": "2.1.1", "@types/jsonwebtoken": "7.2.0", "@types/node": "~14.0.0", "@types/superagent": "4.1.7", "bole": "3.0.2", "bole-console": "^0.1.10", "chai": "4.2.0", "gelf-stream-renewed": "1.2.2", "gulp": "4.0.2", "gulp-clean-compiled-typescript": "1.2.0", "gulp-file": "0.4.0", "gulp-mocha": "6.0.0", "gulp-sourcemaps": "2.6.5", "gulp-tslint": "^8.1.4", "gulp-typescript": "5.0.0", "mocha": "6.0.2", "mocha-typescript": "1.1.17", "reflect-metadata": "^0.1.13", "superagent": "5.1.0", "ts-node": "8.0.2", "tslint": "5.13.1", "typescript": "3.0.1"}, "dependencies": {"@skywind-group/sw-deferred-payment": "1.15.0", "@skywind-group/sw-round-details-report": "^1.0.1", "agentkeepalive": "3.5.2", "fast-xml-parser": "^3.18.0", "https": "1.0.0", "jsonwebtoken": "7.4.3", "superagent-proxy": "2.0.0"}}