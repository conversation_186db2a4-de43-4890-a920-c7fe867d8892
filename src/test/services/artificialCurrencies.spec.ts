import { expect, use } from "chai";
import { getModel } from "../../skywind/models/exchangeRates";
import { ExchangeRatesUpdateJob } from "../../skywind/services/updateJob";
import config from "../../skywind/config";
import { getArtificialRatesRange } from "../../skywind/services/artificialCurrencies";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { omit } from "lodash";
import { logging } from "@skywind-group/sw-utils";
import { DefaultCurrencyProvider } from "../../skywind/providers/defaultCurrencyProvider";

logging.setUpOutput({
    type: "console",
    logLevel: "info"
});

describe("Artificial currencies", () => {
    const originalConfig = config.provider;
    let updateJob: ExchangeRatesUpdateJob;

    before(async () => {
        await getModel().sync();
        await getModel().truncate();
        updateJob = new ExchangeRatesUpdateJob(new DefaultCurrencyProvider());
        await updateJob.init();
    });

    after(() => {
        config.provider = originalConfig;
        updateJob.clean();
    });

    it("get rates for date range with filter", async () => {
        const today = new Date();

        const rates = await getArtificialRatesRange(today, today, { from: "USD", to: "TET" });
        expect(rates.length).to.equal(1);

        const rate = rates[0];
        expect(rate).to.deep.equal({
            artificial: true,
            from: "USD",
            provider: "default",
            providerDate: rate.providerDate,
            rate: 100000000,
            rateDate: rate.rateDate,
            to: "TET",
            type: "ask"
        });
    });

    it("disable GGR", async () => {
        const currencies = Currencies.values().filter(({ disableGGR }) => disableGGR);
        const today = new Date();

        for (const currency of currencies) {
            const rates = await getArtificialRatesRange(today, today, {
                to: currency.code
            });
            expect(rates.length).to.equal(1);

            const rate = rates[0];
            expect(omit(rate, "rate")).to.deep.equal({
                artificial: true,
                disableGGR: true,
                from: rate.from,
                provider: "default",
                providerDate: rate.providerDate,
                rateDate: rate.rateDate,
                to: currency.code,
                type: "ask"
            });
        }
    });

    it("hide GGR", async () => {
        const currencies = Currencies.values().filter(({ disableGGR }) => disableGGR);
        const today = new Date();

        for (const currency of currencies) {
            const rates = await getArtificialRatesRange(today, today, {
                to: currency.code
            }, true);
            expect(rates.length).to.equal(1);

            const rate = rates[0];
            expect(rate).to.deep.equal({
                artificial: true,
                disableGGR: true,
                from: rate.from,
                provider: "default",
                providerDate: rate.providerDate,
                rate: 0,
                rateDate: rate.rateDate,
                to: currency.code,
                type: "ask"
            });
        }
    });
});
