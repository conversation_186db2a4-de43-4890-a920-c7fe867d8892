import { skip, suite, test } from "mocha-typescript";
import { expect } from "chai";
import { getDomain } from "../skywind/utils/getDomain";

@suite
class GetDomainSpec {
    @test
    public testGetDomainFromUrlWithParams() {
        expect(getDomain("http://test.com/v1/version?hello=test")).to.be.equal("http://test.com");
    }

    @test()
    public testInvalidDomain() {
        expect(getDomain("test")).to.be.equal(undefined);
    }

    @test
    public testEmptyDomain() {
        expect(getDomain(undefined)).to.be.equal(undefined);
    }
}
