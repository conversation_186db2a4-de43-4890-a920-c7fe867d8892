import { expect } from "chai";
import { getExternalCurrencyReplacement } from "../skywind/externalCurrencyReplacement";
import { Currencies } from "../skywind/currencies";
import { Currency } from "../skywind/currency";

describe("Currency replacement", () => {
    const externalCurrency = new Currency("BTC", "Bitcoin", {
        code: "BTC",
        minorUnits: 8,
        number: "999"
    });
    const internalCurrency = Currencies.get("UBT");

    it("to internal currency", () => {
        const fn = getExternalCurrencyReplacement({ BTC: "UBT" });
        expect(fn(externalCurrency.code)).to.deep.equal({
            currency: internalCurrency
        });
    });

    it("to internal currency amount", () => {
        const fn = getExternalCurrencyReplacement({ BTC: "UBT" });
        expect(fn(externalCurrency.code, 0.00_015_558)).to.deep.equal({
            currency: internalCurrency,
            amount: 155.58
        });
    });

    it("to external currency", () => {
        const fn = getExternalCurrencyReplacement({ BTC: "UBT" });
        expect(fn(internalCurrency.code)).to.deep.equal({
            currency: externalCurrency
        });
    });

    it("to external currency amount", () => {
        const fn = getExternalCurrencyReplacement({ BTC: "UBT" });
        expect(fn(internalCurrency.code, 155.58)).to.deep.equal({
            currency: externalCurrency,
            amount: 0.00_015_558
        });
    });
});
