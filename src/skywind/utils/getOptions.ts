import { getCertConfig } from "./cert";
import { safeGet } from "@skywind-group/sw-utils";
import { MerchantInfo, HTTPOptions } from "../definitions/common";
import { AdapterConfig } from "../definitions/configOptions";

export function getOptions(merchant: MerchantInfo, config: AdapterConfig, onlySSL: boolean = false): HTTPOptions {
    const options: HTTPOptions = {};

    if (!onlySSL) {
        options.proxy = safeGet(merchant, "proxy", "url") || config.proxy;
        options.timeout = config.timeout;
    }

    const cert = getCertConfig(merchant.code, config.cert);

    if (cert.useCert) {
        options.ssl = cert.settings;
    }

    return options;
}
