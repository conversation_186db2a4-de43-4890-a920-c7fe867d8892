import config from "../config";
import { token as jwt } from "@skywind-group/sw-utils";

const cfgInternal: any = config.internalServerToken;

export async function generateInternalToken<T>(data: T): Promise<string> {
    return generateToken(data, cfgInternal);
}

export async function parseInternalToken(token: string): Promise<any> {
    return verifyToken(token, cfgInternal);
}

export async function generateToken<T>(data: T, cfg: jwt.TokenConfig): Promise<string> {
    return jwt.generate<T>(data, cfg);
}

export function verifyToken<T>(token: string, cfg: jwt.TokenConfig): Promise<T> {
    return jwt.verify<T>(token, cfg);
}
