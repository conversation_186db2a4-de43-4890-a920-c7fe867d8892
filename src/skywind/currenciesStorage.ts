import type { logging } from "@skywind-group/sw-utils";
import { records } from "./currenciesResource";
import { getCurrencies } from "./currenciesService";
import { getCurrencySubscription } from "./currenciesSubscription";
import type { Currency } from "./currency";
import { CurrencyNotFoundError } from "./types";

/* Cache that implemented to prevent multiple parallel queries */
export class CurrenciesStorage {
    private data: Record<string, Currency> = {};
    private updating: Promise<Record<string, Currency>>;
    private updated: boolean;
    private initialized = false;

    constructor(private readonly log: logging.Logger) {
    }

    async init() {
        const subscription = getCurrencySubscription(this.log);
        await subscription?.subscribe({
            onCreate: async (currency) => {
                this.data[currency.code] = currency;
            },
            onUpdate: async (currency) => {
                this.data[currency.code] = currency;
            },
            onDelete: async (code) => {
                delete this.data[code];
            }
        });
        await this.fetch();
        this.initialized = true;
    }

    get(code: string) {
        if (!this.initialized) {
            this.log.warn("Currencies storage is not initialized");
        }
        let currency = this.data[code];
        if (!currency) {
            currency = records[code];
            if (!currency) {
                throw new CurrencyNotFoundError(code);
            }
        }
        return currency;
    }

    keys() {
        return Object.keys(this.data);
    }

    values() {
        return Object.values(this.data);
    }

    private async fetch() {
        if (this.updated) {
            this.updated = false;
            this.updating = undefined;
        }
        if (!this.data) {
            if (!this.updating) {
                this.updating = this.update();
            }
            return this.updating;
        } else {
            return this.data;
        }
    }

    private async update() {
        try {
            const currencies = await getCurrencies();
            this.data = structuredClone(records);
            for (const currency of currencies) {
                this.data[currency.code] = currency;
            }
        } catch (e) {
            this.log.error(e);
        } finally {
            this.updated = true;
        }
        return this.data;
    }
}
