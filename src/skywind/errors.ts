import { SWError, ERROR_LEVEL } from "@skywind-group/sw-wallet-adapter-core";

export class InternalServerError extends SWError {
    constructor(error: Error) {
        super(500, 1, "Internal server error " + JSON.stringify(error), ERROR_LEVEL.ERROR);
    }
}

export class ApiNotFoundError extends SWError {
    constructor() {
        super(404, 2, "API not found", ERROR_LEVEL.INFO);
    }
}

export class ValidationError extends SWError {
    constructor(messages: string | string[]) {
        super(400, 3, `Validation error: ${messages}`, ERROR_LEVEL.WARN);
        this.data.messages = Array.isArray(messages) ? messages.join(", ") : messages;
    }
}

export class MalformedJsonError extends SWError {
    constructor(reason: string) {
        super(400, 674, `Malformed JSON : ${reason}`);
        this.data.reason = reason || "";
    }
}

export class ExchangeRatesNotFoundError extends SWError {
    constructor() {
        super(404, 4, "Exchange rates are not found");
    }
}

export class TokenExpiredException extends SWError {
    constructor() {
        super(403, 5, "Token is expired");
    }
}

export class TokenVerifyException extends SWError {
    constructor() {
        super(403, 6, "Token is not valid");
    }
}
