import type { ProviderExchangeRate } from "../entities/exchangeRates";

export interface ProviderRates {
    provider: string; // "oanda" | "oxr" | "default"
    ts: Date;
    bidRates: {
        [base: string]: ProviderRate;
    };
    askRates: {
        [base: string]: ProviderRate;
    };
}

export interface ProviderRate {
    [currencyCode: string]: number; // ex: "EUR": 1.343542
}

export interface CurrencyProvider {
    getRates(providerDate: Date, baseCurrencies: string[]): Promise<ProviderExchangeRate[]>;
}

export interface CurrencyDatasource {
    load(date: Date, baseCurrencies: string[]): Promise<ProviderRates>;
}
