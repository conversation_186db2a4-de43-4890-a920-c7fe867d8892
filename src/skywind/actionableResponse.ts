import { ExtraData } from "./definitions/common";

export type PopupButtonGameAction = "refresh" | "close" | "lobby" | "cashier" | "continue" | "link" | "game-history" |
    "stopPositions" | "closeWindow";

export const ClientMessageType = {
    ignorable: 0,
    toaster: 1,
    info: 2
};

export const ClientMessageMap = new Map([
    [ClientMessageType.ignorable, "ignorable"],
    [ClientMessageType.toaster, "toaster"],
    [ClientMessageType.info, "info"]]);

export class PopupButtonGameActions {
    public static refresh: PopupButtonGameAction = "refresh";
    public static close: PopupButtonGameAction = "close";
    public static lobby: PopupButtonGameAction = "lobby";
    public static cashier: PopupButtonGameAction = "cashier";
    public static continue: PopupButtonGameAction = "continue";
    public static link: PopupButtonGameAction = "link";
    public static stopPositions: PopupButtonGameAction = "stopPositions";
    public static gameHistory: PopupButtonGameAction = "game-history";
    public static closeWindow: PopupButtonGameAction = "closeWindow";
}

export type PlayerRegulatoryActionAtServer = "resetRealityCheck" | "closeSession" | "cancelBet" |
    "resetSessionLimit" | "customAction" | "setPOPSpainLimits";

export class PlayerRegulatoryActionsAtServer {
    public static resetRealityCheck: PlayerRegulatoryActionAtServer = "resetRealityCheck";
    public static closeSession: PlayerRegulatoryActionAtServer = "closeSession";
    public static cancelBet: PlayerRegulatoryActionAtServer = "cancelBet";

    public static resetSessionLimit: PlayerRegulatoryActionAtServer = "resetSessionLimit";
    public static customAction: PlayerRegulatoryActionAtServer = "customAction";
    public static setPOPSpainLimits: PlayerRegulatoryActionAtServer = "setPOPSpainLimits";
}

export interface ExtraMessage {
    translate: boolean; // flag for game wrapper - do not translate action message and title
    translateAtServer: boolean; // true to translate message and button labels (server purpose only)
    buttons: PopupButton[]; // in the 'action' object there should always be at least one button
    // message to display in case of parent object has no message (SWError parents already have message field)
    message?: string;
    msgType: number; // refer to ClientMessageMap
    msgTitle?: string; // title of message
    translateTitle?: boolean; // flag for game wrapper to override 'translate' value for message title
    // TODO: probably has to be an enum or something like that
    code: number; // unique action code (may be used for translation)

    // message may contain entries, that may change in different scenarios, so we provide a possibility for game client
    // to translate and replace them. Example: "This is a kind reminder that you have played for {key1} {key2} already"
    textReplacements?: { [type: string]: string | number; };
}

export class ExtraMessageImpl implements ExtraMessage {
    public translate: boolean;
    public translateAtServer: boolean;
    public buttons: PopupButtonImpl[];
    public message?: string;
    public code: number;
    public msgType: number; // refer to ClientMessageMap
    public translateTitle?: boolean;
    public msgTitle?: string;
    public textReplacements?: { [type: string]: string | number; };

    constructor() {
        this.buttons = [];
    }

    public static create(): ExtraMessageImpl {
        return new ExtraMessageImpl();
    }

    public addButton(button: PopupButtonImpl): ExtraMessageImpl {
        this.buttons.push(button);
        return this;
    }

    public setButtons(buttons: PopupButtonImpl[]): ExtraMessageImpl {
        this.buttons = buttons;
        return this;
    }

    public setTranslate(translate: boolean): ExtraMessageImpl {
        this.translate = translate;
        return this;
    }

    public setTranslateAtServer(value: boolean): ExtraMessageImpl {
        this.translateAtServer = value;
        return this;
    }

    public setMessage(message: string): ExtraMessageImpl {
        this.message = message;
        return this;
    }

    public setMessageTitle(messageTitle: string): ExtraMessageImpl {
        this.msgTitle = messageTitle;
        return this;
    }

    public setTranslateTitle(translate: boolean): ExtraMessageImpl {
        this.translateTitle = translate;
        return this;
    }

    public setMessageType(value: number): ExtraMessageImpl {
        if (value) {
            this.msgType = value;
        }
        return this;
    }

    public setCode(value: number): ExtraMessageImpl {
        this.code = value;
        return this;
    }

    public addTextReplacement(keyName: string, keyValue: string|number): ExtraMessageImpl {
        if (!this.textReplacements) {
            this.textReplacements = {};
        }
        this.textReplacements[keyName] = keyValue;
        return this;
    }
}

export interface PopupButton {
    label: string;
    // serverCall must be specified if a player decision is to be sent to server in accordance with regulatory
    // requirement
    serverCall: PlayerActionServerCallImpl;
    gameAction: PopupButtonGameAction;
    translate?: boolean; // specify to override behaviour of parent object's translate field
    noClose?: boolean; // set true to notify wrapper that popup should not be closed upon pressing this button
}

export interface Linkable {
    link: string;
    openLinkInNewTab?: boolean;
    setLink(value): this;
    setOpenLinkInNewTab(value: boolean): this;
}

export class PopupButtonImpl implements PopupButton {
    public label: string;
    public serverCall: PlayerActionServerCallImpl;
    public gameAction: PopupButtonGameAction;
    public translate?: boolean;
    public noClose?: boolean;

    public static create(): PopupButtonImpl {
        return new PopupButtonImpl();
    }

    public setLabel(value: string): PopupButtonImpl {
        this.label = value;
        return this;
    }

    public setServerCall(serverCall: PlayerActionServerCallImpl): PopupButtonImpl {
        this.serverCall = serverCall;
        return this;
    }

    public setGameAction(value: PopupButtonGameAction): PopupButtonImpl {
        this.gameAction = value;
        return this;
    }

    public setTranslate(translate: boolean): PopupButtonImpl {
        this.translate = translate;
        return this;
    }

    public setNoClose(value: boolean): PopupButtonImpl {
        this.noClose = value;
        return this;
    }
}

export class LinkButtonImpl extends PopupButtonImpl implements Linkable {
    public link: string;
    public openLinkInNewTab: boolean = true;

    public static create(): PopupButtonImpl & Linkable {
        const button = new LinkButtonImpl();
        button.setGameAction(PopupButtonGameActions.link);
        return button;
    }

    public setOpenLinkInNewTab(value: boolean): this {
        this.openLinkInNewTab = value;
        return this;
    }

    public setLink(value): this {
        this.link = value;
        return this;
    }
}

export interface PlayerActionServerCallParams {
    [field: string]: any;
    regulation?: string;
}

export interface PlayerActionServerCall {
    regulatoryAction: PlayerRegulatoryActionAtServer; // regulatory action to perform at server side
    params: PlayerActionServerCallParams; // params to send within body - token,  player/game/regulatory data & etc
}

export class PlayerActionServerCallImpl implements PlayerActionServerCall {
    public regulatoryAction: PlayerRegulatoryActionAtServer;
    public params: PlayerActionServerCallParams;

    public static create(): PlayerActionServerCallImpl {
        return new PlayerActionServerCallImpl();
    }

    public setRegulatoryAction(value: PlayerRegulatoryActionAtServer): PlayerActionServerCallImpl {
        this.regulatoryAction = value;
        return this;
    }

    public setParams(value: PlayerActionServerCallParams): PlayerActionServerCallImpl {
        this.params = value;
        return this;
    }
}

export function getExtraMessageTranslatorKey (extraMessage: ExtraMessage) {
    return `EXTRA_MESSAGE_CODE_${extraMessage.code}`;
}

export function getButtonLabelTranslatorKey(button: PopupButton) {
    return `ACTION_BUTTON_LABEL_${button.gameAction.toUpperCase()}`;
}

export interface MrchExtraData extends ExtraData {
    useServerMessage?: boolean; // flag for wrapper that should forbid wrapper to translate error contents
    messageArray?: ExtraMessage[];

    // 0 - don't send error from server at all (error will not be returned to client),
    // 1 - don't show error nor to player nor in console, 2 - show error in console only,
    // 3 - show error to player and not in console, 4 - show error both to player and in console
    errorVisibility?: number;
}

export class MrchExtraDataImpl implements MrchExtraData {
    public useServerMessage: boolean;
    public messageArray?: ExtraMessage[];
    public errorVisibility?: number;

    private constructor() {
    }

    public setUseServerMessage(value: boolean): MrchExtraDataImpl {
        this.useServerMessage = value;
        return this;
    }

    public addExtraMessage(value: ExtraMessage): MrchExtraDataImpl {
        if (!this.messageArray) {
            this.messageArray = [];
        }
        this.messageArray.push(value);
        return this;
    }

    public setErrorVisibility(value: number): MrchExtraDataImpl {
        this.errorVisibility = value;
        return this;
    }

    public static create(): MrchExtraDataImpl {
        return new MrchExtraDataImpl();
    }
}
