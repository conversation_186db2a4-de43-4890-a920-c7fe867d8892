import * as request from "superagent";
import * as superAgentProxySupport from "superagent-proxy";
import config from "../config";
import { HTTPOptions } from "../..";
import * as https from "https";
import { ConnectionError, MerchantAdapterAPIError, MerchantAdapterAPITransientError, SWError } from "../errors";
import { SuperAgentRequest } from "superagent";
import { getSecuredObjectData } from "../..";
import { logging, safeGet } from "@skywind-group/sw-utils";
import Logger = logging.Logger;
import { BaseUrlIncludeQuestionMarkInfo } from "../definitions/common";
const { adapterAPI: { keepAlive } } = config;
const keepAliveAgent = require("agentkeepalive");
const URL = require("url").URL;

const httpAgent = keepAlive.maxFreeSockets && new keepAliveAgent(keepAlive);
const httpsAgent = new https.Agent(keepAlive.maxFreeSockets && new keepAliveAgent(keepAlive));

superAgentProxySupport(request);

export interface SuperAgentResponse {
    request: {
        url: string;
        _data: object;
        qs: object;
        header: object;
    };
    status: number;
    body: any;
    text: string;
}

export interface RequestInfo {
    data?: object;
    url: string;
    ts: number;
    method: string;
    proxy?: string;
}

export class BaseHttpService {
    private readonly agent: any;
    private readonly secureKeys: string[];

    constructor(public readonly baseURL: string,
                private appendedParams?: object,
                private baseOptions?: HTTPOptions,
                protected logger: Logger = logging.logger("http"),
                secureKeys?: string[]
    ) {

        this.agent = baseURL.includes("https") ? httpsAgent : httpAgent;
        this.secureKeys = secureKeys || config.logParams.secureKeys;
    }

    public async get<Response>(url: string, data: object, options: HTTPOptions = {}): Promise<Response> {
        const dataToSend = this.appendParamsToRequest(data);
        const fullUrl = this.getFullUrl(url);

        const requestInfo = {
            data: getSecuredObjectData(dataToSend, this.secureKeys),
            url: fullUrl,
            ts: Date.now(),
            method: "GET",
            proxy: options.proxy
        };

        this.logger.info(requestInfo, `${requestInfo.method} ${requestInfo.url}`);

        const req = request
            .get(fullUrl)
            .agent(this.agent)
            .auth(options.username, options.password)
            .accept("application/json")
            .query(dataToSend);

        this.addHeaders(req, options);
        this.addSSLData(req, options);

        return this.process(req, requestInfo);
    }

    public async post<Response>(url: string, data: object, options: HTTPOptions = {}): Promise<Response> {
        const dataToSend = this.appendParamsToRequest(data);
        const fullUrl = this.getFullUrl(url);

        const requestInfo = {
            data: getSecuredObjectData(dataToSend, this.secureKeys),
            url: fullUrl,
            ts: Date.now(),
            method: "POST",
            proxy: options.proxy
        };

        this.logger.info(requestInfo, `${requestInfo.method} ${requestInfo.url}`);

        const req = request
            .post(fullUrl)
            .agent(this.agent);

        await this.decoratePOSTRequest(req, options);
        this.addHeaders(req, options);
        this.addSSLData(req, options);

        req.send(dataToSend);

        return this.process(req, requestInfo);
    }

    public async put<Response>(url: string, data: object, options: HTTPOptions = {}): Promise<Response> {
        const dataToSend = this.appendParamsToRequest(data);
        const fullUrl = this.getFullUrl(url);

        const requestInfo = {
            data: getSecuredObjectData(dataToSend, this.secureKeys),
            url: fullUrl,
            ts: Date.now(),
            method: "PUT",
            proxy: options.proxy
        };

        this.logger.info(requestInfo, `${requestInfo.method} ${requestInfo.url}`);

        const req = request
            .put(fullUrl)
            .agent(this.agent)
            .auth(options.username, options.password)
            .accept("application/json");

        this.addHeaders(req, options);
        this.addSSLData(req, options);

        req.send(dataToSend);

        return this.process(req, requestInfo);
    }

    public async delete<Response>(url: string, data?: object, options: HTTPOptions = {}): Promise<Response> {
        const dataToSend = this.appendParamsToRequest(data);
        const fullUrl = this.getFullUrl(url);

        const requestInfo = {
            data: getSecuredObjectData(dataToSend, this.secureKeys),
            url: fullUrl,
            ts: Date.now(),
            method: "DELETE",
            proxy: options.proxy
        };

        this.logger.info(requestInfo, `${requestInfo.method} ${requestInfo.url}`);

        const req = request
            .del(fullUrl)
            .agent(this.agent)
            .auth(options.username, options.password)
            .accept("application/json");

        this.addHeaders(req, options);
        this.addSSLData(req, options);

        req.send(dataToSend);

        return this.process(req, requestInfo);
    }

    protected async decoratePOSTRequest(req: SuperAgentRequest, options: HTTPOptions = {}) {
        req
            .auth(options.username, options.password)
            .accept("application/json");

    }

    /**
     * crutch for some operators (which are integrated via seamless adapter)
     * some operators need to put a question mark at the end of the baseUrl
     * baseUrl -> merchant.params.serverUrl
     * this is unusual behavior for url, due to this need this additional function
     * https://jira.skywindgroup.com/browse/SWS-35990
     */
    private isBaseUrlIncludeQuestionMark(): BaseUrlIncludeQuestionMarkInfo {
        const isBaseUrlIncludeQuestion = this.baseURL.includes("?");
        let baseUrlIncludeQuestionMarkInfo = {
            isBaseUrlIncludeQuestion,
            baseUrlWithoutQuestionMark: this.baseURL,
            partWithQuestionMark: ""
        };
        if (isBaseUrlIncludeQuestion) {
            // trim last slash
            const baseUrlWithoutLastSlash = this.baseURL.endsWith("/") ? this.baseURL.slice(0, - 1) : this.baseURL;
            // find last index of slash before question mark
            const lastSlashIndex = baseUrlWithoutLastSlash.lastIndexOf("/");
            baseUrlIncludeQuestionMarkInfo = {
                isBaseUrlIncludeQuestion,
                baseUrlWithoutQuestionMark: baseUrlWithoutLastSlash.slice(0, lastSlashIndex),
                partWithQuestionMark: baseUrlWithoutLastSlash.slice(lastSlashIndex)
            };
        }

        return baseUrlIncludeQuestionMarkInfo;
    }

    private getFullUrl(url: string) {
        const {
            isBaseUrlIncludeQuestion, baseUrlWithoutQuestionMark, partWithQuestionMark
        } = this.isBaseUrlIncludeQuestionMark();

        const preparedBaseUrl =
            baseUrlWithoutQuestionMark.endsWith("/") ? baseUrlWithoutQuestionMark : baseUrlWithoutQuestionMark + "/";
        const preparedUrl = url.startsWith("/") ? url.substring(1) : url;
        let resultUrl = new URL(preparedUrl, preparedBaseUrl).toString();

        if (isBaseUrlIncludeQuestion) {
            resultUrl = resultUrl
                .replace(baseUrlWithoutQuestionMark, `${baseUrlWithoutQuestionMark}${partWithQuestionMark}`);
        }
        return resultUrl;
    }

    protected addSSLData(req: request.SuperAgentRequest, options: HTTPOptions) {
        if ("ssl" in options) {
            if (options.ssl.cert) {
                req.cert(Buffer.from(options.ssl.cert));
            }

            if (options.ssl.key) {
                req.key(Buffer.from(options.ssl.key));
            }

            if (options.ssl.ca) {
                req.ca(Buffer.from(options.ssl.ca));
            }

            // seems the only way to send a passphrase with superagent is by using pfx
            // (even thou pfx by itself might not be provuded)
            if (options.ssl.password) {
                const pfx = options.ssl.pfx ? Buffer.from(options.ssl.pfx) : "";
                req.pfx({ pfx, passphrase: options.ssl.password });
            }
        }
    }

    protected addHeaders(req: request.SuperAgentRequest, opts: HTTPOptions) {
        const options = this.baseOptions ? { ...this.baseOptions, ...opts } : opts;

        if (options.proxy) {
            (req as any).proxy(options.proxy);
        }

        if (options.auth) {
            if (options.auth.internalToken) {
                req.set("x-internal-token", options.auth.internalToken);
            }
            if (options.auth.accessToken) {
                req.set("x-access-token", options.auth.accessToken);
            }
        }

        if ("timeout" in options) {
            req.timeout(options.timeout);
        }
    }

    private appendParamsToRequest(req: object) {
        return { ...req, ...this.appendedParams };
    }

    protected async process(sendRequestPromise: request.SuperAgentRequest, requestInfo: RequestInfo): Promise<any> {
        // when trying to use async/await with SuperAgent request we are missing CLS context
        return sendRequestPromise
            .catch((err) => this.processResponse(err.response, requestInfo, err))
            .then((res) => this.processResponse(res, requestInfo));
    }

    protected async processResponse(response: SuperAgentResponse,
                                    requestInfo: RequestInfo,
                                    error?: Error): Promise<any> {
        if (error && !response) {
            const connError = new ConnectionError(this.baseURL, error as SWError);
            let decodedPacket;
            // If a raw packet is sent by node, decode it and append it to the logged error
            // This is done mostly to help us debug node httpParser errors related to request headers
            if (error["rawPacket"]) {
                decodedPacket = error["rawPacket"].toString();
            }
            this.logger.error({ ...connError, decodedPacket }, "Connection error");
            return Promise.reject(connError);
        }

        this.logger.debug({
            url: requestInfo.url,
            request: requestInfo.data,
            responseStatus: response.status,
            response: response.body,
            requestTime: this.getRequestTime(requestInfo.ts)
        }, "Merchant API request/response");

        if (response.status < 200 || response.status >= 300) {
            const isSWError: boolean = SWError.isSWError(response.body);
            if (isSWError) {
                const swError = new SWError(
                    response.status,
                    response.body.code,
                    response.body.message,
                    response.body.errorLevel,
                    response.body.extraData,
                    response.body.data)
                    .setProviderDetails(response.body.providerDetails);

                const translate: boolean = safeGet(response.body, "translate");
                if (!translate) {
                    swError.dontTranslate();
                }
                return Promise.reject(swError);
            }

            if (response.status >= 400 && response.status < 500) {
                return Promise.reject(new MerchantAdapterAPIError(this.baseURL, response.body, response.status));
            } else {
                return Promise.reject(new MerchantAdapterAPITransientError(this.baseURL, response.body));
            }
        } else {
            return response.body;
        }
    }

    protected getRequestTime(ts: number): number {
        return (Date.now() - ts) / 1000;
    }
}
