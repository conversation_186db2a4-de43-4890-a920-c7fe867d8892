import {
    FinalizeGameRequest,
    GameLoginRequest,
    GameLogoutRequest,
    GameLogoutResponse,
    MrchRoundResolveRequest,
    MrchRoundResolveResponse
} from "../definitions/brokenGame";
import { MerchantGameTokenData } from "../definitions/startGame";
import { BaseHttpService } from "./baseHTTPService";
import { MerchantInfo } from "../definitions/common";
import { Balance } from "../definitions/balance";
import { MERCHANT_REGULATION } from "../..";

export interface MerchantBrokenGameService<AUTHT extends MerchantGameTokenData = MerchantGameTokenData> {

    logoutGame?(merchant: MerchantInfo, gameTokenData: AUTHT, request: GameLogoutRequest): Promise<GameLogoutResponse>;
    loginGame?(merchant: MerchantInfo, gameTokenData: AUTHT, request: GameLoginRequest): Promise<void>;
    finalizeGame(merchant: MerchantInfo, gameTokenData: AUTHT, request: FinalizeGameRequest): Promise<Balance | void>;
}

export abstract class DefaultMerchantBrokenGameService<AUTHT extends MerchantGameTokenData = MerchantGameTokenData>
    implements MerchantBrokenGameService <AUTHT> {

    public abstract finalizeGame(merchant: MerchantInfo, gameTokenData: AUTHT,
                                 request: FinalizeGameRequest): Promise<Balance | void>;
}

export class HTTPMerchantBrokenGameService<AUTHT extends MerchantGameTokenData = MerchantGameTokenData>
    extends BaseHttpService implements MerchantBrokenGameService <AUTHT> {

    constructor(adapterUrl: string, merchantInfo: MerchantInfo, regulation?: MERCHANT_REGULATION) {
        super(adapterUrl, { merchantInfo, regulation });
    }

    public async logoutGame(merchant: MerchantInfo,
                            gameTokenData: AUTHT,
                            request: GameLogoutRequest): Promise<GameLogoutResponse> {
        return this.post<GameLogoutResponse>("logoutGame", { gameTokenData, request });
    }

    public async loginGame(merchant: MerchantInfo, gameTokenData: AUTHT, request: GameLoginRequest): Promise<void> {
        await this.post("loginGame", { gameTokenData, request });
    }

    public async finalizeGame(merchant: MerchantInfo, gameTokenData: AUTHT,
                              request: FinalizeGameRequest): Promise<Balance | void> {
        return this.post<void>("finalizeGame", { gameTokenData, request });
    }
}
