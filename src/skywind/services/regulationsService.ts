import {
    MerchantGameTokenData,
} from "../definitions/startGame";
import { PlayerRegulatoryActionRequest, ReportCriticalFilesToMerchantRequest } from "../definitions/regulation";
import { BaseHttpService } from "./baseHTTPService";
import { MerchantInfo } from "../definitions/common";
import { MERCHANT_REGULATION } from "../..";

export interface MerchantRegulationsService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData> {

    performRegulatoryAction?(merchant: MerchantInfo,
                             gameTokenData: AUTHT,
                             request: PlayerRegulatoryActionRequest): Promise<any>;
    reportCriticalFiles(merchant: MerchantInfo, req: ReportCriticalFilesToMerchantRequest): Promise<void>;
}

export abstract class DefaultMerchantRegulationsService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData>
    implements MerchantRegulationsService <AUTHT> {

    public abstract reportCriticalFiles(merchant: MerchantInfo,
                                        req: ReportCriticalFilesToMerchantRequest): Promise<void>;
}

export class HTTPMerchantRegulationsService <AUTHT extends MerchantGameTokenData = MerchantGameTokenData>
    extends BaseHttpService implements MerchantRegulationsService <AUTHT> {

    constructor(adapterUrl: string, merchantInfo: MerchantInfo, regulation?: MERCHANT_REGULATION) {
        super(adapterUrl, { merchantInfo, regulation });
    }

    public async performRegulatoryAction(merchant: MerchantInfo,
                                         gameTokenData: AUTHT,
                                         request: PlayerRegulatoryActionRequest): Promise<any> {
        return this.post("performRegulatoryAction", { gameTokenData, request });
    }

    public async reportCriticalFiles(merchant: MerchantInfo,
                                     req: ReportCriticalFilesToMerchantRequest): Promise<void> {

        await this.post("reportCriticalFiles", { request: req });
    }
}
