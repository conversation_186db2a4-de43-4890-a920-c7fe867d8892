import * as schedule from "node-schedule";
import { getNextDay, getPrevDay, getToday } from "../providers/utils";
import * as Model from "../models/exchangeRates";
import type { ExchangeRate } from "../entities/exchangeRates";
import type { CurrencyProvider } from "../providers/provider";
import { DefaultCurrencyProvider } from "../providers/defaultCurrencyProvider";
import { measures } from "@skywind-group/sw-utils";
import config from "../config";
import logger from "../logger";
import { ApplicationLock, ApplicationLockId } from "../models/applicationLock";
import { sequelize as db } from "../models/db";
import { MergeCurrencyProvider } from "../providers/mergeCurrencyProvider";
import measure = measures.measure;
import measureProvider = measures.measureProvider;

const log = logger();

export class ExchangeRatesUpdateJob {
    private updateJob: { cancel: () => void; };
    private updateTimeoutId: null | NodeJS.Timeout;

    constructor(private readonly provider: CurrencyProvider) {
    }

    public async init() {
        const today = getToday();
        await this.update(getPrevDay(today, 2), today);
        await this.update(getPrevDay(), getNextDay());

        log.info("Schedule exchange rates updates %s", config.job.updateSchedule);
        this.updateJob = schedule.scheduleJob(config.job.updateSchedule, () => {
            measureProvider.runInTransaction("ExchangeRatesUpdateJob.scheduledJob", async () => {
                const prevDate = getPrevDay();
                const nextDate = getNextDay();

                try {
                    await this.execWithRetry(
                        (): any => this.update(prevDate, nextDate), Date.now() + config.job.updateFallbackTimeout,
                        config.job.updateRetryTimeout);
                } catch (err) {
                    log.error(err, "Failed to update exchange rates from provider. Fallback to previous date");
                    try {
                        await this.execWithRetry(
                            () => this.fallbackUpdate(nextDate), nextDate.getTime(),
                            config.job.fallbackRetryTimeout);
                    } catch (err) {
                        log.error(err, "Failed to fallback to update exchange rates.");
                    }
                }
            });
        });
    }

    public async load(rateDate: Date, baseCurrencies = config.baseCurrencies): Promise<ExchangeRate[]> {
        log.info("Load exchange rates for %s for %s", rateDate, baseCurrencies);

        const providerDate = getPrevDay(rateDate, 2);
        const rates = await this.getRates(providerDate, rateDate, baseCurrencies);

        await Model.saveRates(rates);

        log.info("Exchange rates for %s for %s loaded", rateDate, baseCurrencies);

        return rates;
    }

    public clean() {
        clearTimeout(this.updateTimeoutId);
        this.updateTimeoutId = null;
        if (this.updateJob) {
            this.updateJob.cancel();
        }
    }

    private async execWithRetry(func: () => Promise<void>, retryExpire: number, retryTimeout: number): Promise<void> {
        if (Date.now() > retryExpire) {
            return Promise.reject(new Error("Retry attempts are exhausted"));
        }
        try {
            await func();
        } catch (err) {
            log.warn(err, "Failed to execute. Retry in %d", retryTimeout);
            return new Promise<void>((resolve, reject) => {
                this.updateTimeoutId = setTimeout(() => {
                    return this.execWithRetry(func, retryExpire, retryTimeout).then(resolve).catch(reject);
                }, retryTimeout);
            });
        }
    }

    @measure({ name: "ExchangeRatesUpdateJob.update", isAsync: true })
    private async update(providerDate: Date, rateDate: Date): Promise<void> {
        await db.transaction(async (transaction) => {

            await ApplicationLock.lock(transaction, ApplicationLockId.UPDATE_RATES);

            try {
                await this.doUpdate(providerDate, rateDate);
            } finally {
                await ApplicationLock.unlock(transaction, ApplicationLockId.UPDATE_RATES);
            }
        });
    }

    private async doUpdate(providerDate: Date, rateDate: Date): Promise<void> {
        log.info("Update exchange rates for %s", rateDate);

        if (await Model.hasRates(rateDate)) {
            log.info("Exchange rates already updated for %s", rateDate);
            return;
        }

        const rates = await this.getRates(providerDate, rateDate, config.baseCurrencies);

        await Model.saveRates(rates);

        log.info("Exchange rates for %s updated", rateDate);
    }

    private async getRates(providerDate: Date, rateDate: Date, baseCurrencies: string[]): Promise<ExchangeRate[]> {
        const providerRates = await this.provider.getRates(providerDate, baseCurrencies);
        return providerRates.map((rate) => ({ ...rate, rateDate }));
    }

    @measure({ name: "ExchangeRatesUpdateJob.fallbackUpdate", isAsync: true })
    private async fallbackUpdate(rateDate: Date): Promise<void> {
        log.info("Fallback to update exchange rates for %s", rateDate);

        const rates = await Model.getRates(getPrevDay(rateDate));

        for (const rate of rates) {
            rate.rateDate = rateDate;
        }

        await Model.saveRates(rates);

        log.info("Exchange rates for %s updated", rateDate);
    }
}

let updateJob: ExchangeRatesUpdateJob;

export async function initUpdateJob(): Promise<ExchangeRatesUpdateJob> {
    if (updateJob) {
        return updateJob;
    }
    let provider: CurrencyProvider;
    if (["oanda", "oxr"].includes(config.provider)) {
        provider = new MergeCurrencyProvider();
    } else {
        provider = new DefaultCurrencyProvider();
    }
    updateJob = new ExchangeRatesUpdateJob(provider);
    await updateJob.init();
    return updateJob;
}
