import * as Parser from "fast-xml-parser";
import { logging } from "@skywind-group/sw-utils";
import { J2xOptionsOptional, X2jOptionsOptional } from "fast-xml-parser";
import { ConvertToXmlError, XmlParseError } from "../errors";

const log = logging.logger("xml-service");

export class XmlService {
    private xmlToObjOptions: X2jOptionsOptional = {
        ignoreAttributes: false,
        parseAttributeValue: true,
        attributeNamePrefix: "_"
    };

    private objToXmlOptions: J2xOptionsOptional = {
        attributeNamePrefix: "@_",
        attrNodeName: "@", // default is false
        textNodeName: "#text",
        ignoreAttributes: true,
        cdataTagName: "__cdata", // default is false
        cdataPositionChar: "\\c",
        format: false,
        indentBy: "    ",
        supressEmptyNode: false
    };

    private readonly parser: any;

    constructor(xmlToObjOptions?: X2jOptionsOptional, objToXmlOptions?: J2xOptionsOptional) {
        this.xmlToObjOptions = xmlToObjOptions || this.xmlToObjOptions;
        this.objToXmlOptions = objToXmlOptions || this.objToXmlOptions;
        this.parser = new Parser.j2xParser(this.objToXmlOptions);
    }

    public convertToXML(objectData: any = {}): string {
        try {
            return this.parser.parse(objectData);
        } catch (error) {
            const convertError = new ConvertToXmlError(objectData);
            log.error(convertError, "convert to XML error");
            throw convertError;
        }
    }

    public convertToObject(xml: string): any {
        try {
            return Parser.parse(xml, this.xmlToObjOptions, true);
        } catch (error) {
            const xmlError = new XmlParseError(xml);
            log.error(xmlError, "XML parse error");
            throw xmlError;
        }
    }
}
