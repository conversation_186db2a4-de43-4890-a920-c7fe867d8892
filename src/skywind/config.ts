export default {
    logLevel: process.env.LOG_LEVEL || "debug",

    adapterAPI: {
        keepAlive: {
            maxFreeSockets: +process.env.ADAPTER_API_KEEP_ALIVE_FREE_SOCKET_COUNT || 100,
            freeSocketTimeout: +process.env.ADAPTER_API_KEEP_ALIVE_TIMEOUT || 30000,
            socketActiveTTL: +process.env.ADAPTER_API_SOCKET_ACTIVE_TTL || 60000,
        }
    },

    internalServerToken: {
        expiresIn: +process.env.INTERNAL_SERVER_TOKEN_EXPIRES_IN || 300,
        algorithm: process.env.INTERNAL_SERVER_TOKEN_ALGORITHM || "HS256",
        issuer: process.env.INTERNAL_SERVER_TOKEN_ISSUER || "skywindgroup",
        secret: process.env.INTERNAL_SERVER_TOKEN_SECRET || "TU8N9oP4pPfrUMaRYkjwBsOyw0hgg39sPsTjONrgnN1ErJbn2"
    },

    logParams: {
        secureKeys: [
            "password", "newPassword", "key", "token", "accessToken", "secretKey", "ticket", "merch_pwd",
            "cust_session_id", "Authorization", "Password"
        ]
    },

    graylog: {
        host: process.env.GRAYLOG_HOST || undefined,
        port: +process.env.GRAYLOG_PORT || undefined,
    },
};
