import * as Sequelize from "sequelize";
import type { ExchangeRate, ExchangeRateFilter } from "../entities/exchangeRates";
import { sequelize as db } from "./db";
import { ExchangeRateType } from "@skywind-group/sw-currency-exchange";
import { uniq } from "lodash";

export interface ExchangeRateDBInstance extends Sequelize.Model<
    Sequelize.InferAttributes<ExchangeRateDBInstance>,
    Sequelize.InferCreationAttributes<ExchangeRateDBInstance>
>, ExchangeRate {
}

export type ExchangeRateModel = Sequelize.ModelStatic<ExchangeRateDBInstance>;

const model: ExchangeRateModel = db.define<ExchangeRateDBInstance, ExchangeRate>(
    "exchange_rates",
    {
        from: {
            field: "from_currency",
            type: Sequelize.DataTypes.STRING,
            allowNull: false,
            primaryKey: true
        },
        to: {
            field: "to_currency",
            type: Sequelize.DataTypes.STRING,
            allowNull: false,
            primaryKey: true
        },
        rate: {
            type: Sequelize.DataTypes.DECIMAL,
            allowNull: false
        },
        rateDate: {
            field: "rate_date",
            type: Sequelize.DataTypes.DATEONLY,
            allowNull: false,
            primaryKey: true
        },
        providerDate: {
            field: "provider_date",
            type: Sequelize.DataTypes.DATEONLY,
            allowNull: false
        },
        provider: {
            type: Sequelize.DataTypes.STRING,
            allowNull: false
        },
        type: {
            type: Sequelize.DataTypes.ENUM,
            values: [ExchangeRateType.BID, ExchangeRateType.ASK],
            allowNull: false,
            defaultValue: ExchangeRateType.BID,
            primaryKey: true
        }
    },
    {
        underscored: true,
        updatedAt: false,
        indexes: [
            {
                fields: ["rate_date", "type"],
                name: "idx_exchange_rates_rate_date_type"
            }
        ]
    }
);

export function getModel(): ExchangeRateModel {
    return model;
}

export async function hasRates(rateDate: Date): Promise<boolean> {
    const item = await model.findOne({ where: { rateDate } });
    return !!item;
}

export async function saveRates(rates: ExchangeRate[]): Promise<void> {
    try {
        await model.bulkCreate(rates, { ignoreDuplicates: true });
    } catch (err) {
        if (err instanceof Sequelize.UniqueConstraintError) {
            // rates already updated
            return;
        }
        return Promise.reject(err);
    }
}

export async function getRates(rateDate: Date, query: ExchangeRateFilter = {}): Promise<ExchangeRate[]> {
    return findRates({
        rateDate,
        ...toFilter(query)
    });
}

export async function getRatesRange(startDate: Date, endDate: Date, query: ExchangeRateFilter = {}): Promise<ExchangeRate[]> {
    return findRates({
        rateDate: {
            [Sequelize.Op.gte]: startDate,
            [Sequelize.Op.lte]: endDate
        },
        ...toFilter(query)
    });
}

export async function findRates(where: Sequelize.WhereOptions<ExchangeRate>): Promise<ExchangeRate[]> {
    return (await model.findAll({
        attributes: { exclude: ["createdAt"] },
        where,
        order: [["rateDate", "ASC"]]
    })).map((item) => item.toJSON());
}

function toFilter(query: ExchangeRateFilter): ExchangeRateFilter {
    const filter: ExchangeRateFilter = {};
    if (query.type) {
        filter.type = query.type;
    }
    if (query.from) {
        filter.from = query.from;
    }
    if (query.to) {
        filter.to = query.to;
    }
    return filter;
}
