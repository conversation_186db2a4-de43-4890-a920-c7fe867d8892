export interface Limits {
    stakeAll: number[];
    stakeDef: number;
    stakeMax: number;
    stakeMin: number;
}

export interface GameFeatures {
    live?: object;
    liveRush?: object;
    [type: string]: any;
}
export enum SkywindGameTypes {
    Slot = "slot",
    Action = "action",
    Table = "table",
    External = "external"
}

export interface SWGameInfo {
    code: string;
    url: string;
    title: string;
    settings: {
        jackpotId: {
            [type: string]: string;
        }
    };
    features?: GameFeatures;
    limits?: {
        [field: string]: Limits;
    };
    totalBetMultiplier?: number;
    jackpots?: SWJackpotTickerMapping;
    status: string;
    type: SkywindGameTypes;
    info: any;
    defaultInfo: { name: string; description: string; };
}

export interface SWJackpotTickerMapping {
    [jackpotId: string]: SWJackpotTicker;
}

export interface SWJackpotTicker {
    jackpotId: string;
    jackpotType: string;
    pools: {
        [name: string]: {
            amount: number;
        }
    };
}
