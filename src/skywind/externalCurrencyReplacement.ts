import { Currencies } from "./currencies";
import { Currency } from "./currency";

const configs: Record<string, ExternalCurrencyConfig> = require("./resources/external_currencies.json");
const externalCurrencies = new Map<string, ExternalCurrencyData>(Object.entries(configs)
    .reduce((result, [ code, { name, minorUnits, internalCurrencyMultipliers } ]) => {
        const internal = toInternalCurrencies(internalCurrencyMultipliers);
        if (Object.keys(internal).length) {
            const currency = new Currency(code, name, { code, number: "999", minorUnits });
            return [ ...result, [ code, { currency, internalCurrencies: internal } ] ];
        }
        return result;
    }, []));

function toInternalCurrencies(currencies: Record<string, number>): Record<string, InternalCurrencyData> {
    return Object.entries(currencies).reduce<Record<string, InternalCurrencyData>>((result, [ code, multiplier ]) => {
        const currency = Currencies.value(code);
        if (currency) {
            return {
                ...result,
                [code]: {
                    currency,
                    multiplier
                }
            };
        }
        return result;
    }, {});
}

interface ExternalCurrencyConfig {
    name: string;
    minorUnits: number;
    internalCurrencyMultipliers: Record<string, number>;
}

interface InternalCurrencyData {
    currency: Currency;
    multiplier: number;
}

interface ExternalCurrencyData {
    currency: Currency;
    internalCurrencies: Record<string, InternalCurrencyData>;
}

interface ReplacementData extends InternalCurrencyData {
    external: boolean;
}

export interface CurrencyAmount {
    currency?: Currency;
    amount?: number;
}

type ExternalCurrencyReplacement = (currency: string | undefined, amount?: number) => CurrencyAmount;

export function getExternalCurrencyReplacement(
    config?: Record<string, string> | Array<[ string, string ]>
): ExternalCurrencyReplacement {
    const data = Array.isArray(config) ? config : Object.entries(config || {});
    const replacements = new Map<string, ReplacementData>(data.reduce((result, [ external, internal ]) => {
        const externalData = externalCurrencies.get(external);
        if (externalData) {
            const internalData = externalData.internalCurrencies[internal];
            if (internalData) {
                return [
                    ...result,
                    [
                        externalData.currency.code,
                        {
                            currency: internalData.currency,
                            multiplier: internalData.multiplier,
                            external: true
                        }
                    ],
                    [
                        internalData.currency.code,
                        {
                            currency: externalData.currency,
                            multiplier: internalData.multiplier,
                            external: false
                        }
                    ]
                ];
            }
        }
        return result;
    }, []));
    if (Array.from(replacements.keys()).length) {
        return (currencyCode, amount) => {
            if (!currencyCode) {
                return amount ? { amount } : {};
            }
            const replacement = replacements.get(currencyCode);
            if (!replacement) {
                return {
                    currency: Currencies.value(currencyCode),
                    ...(amount ? { amount } : {})
                };
            }
            if (!amount) {
                return {
                    currency: replacement.currency,
                };
            }
            const newAmount = replacement.external ? amount / replacement.multiplier : amount * replacement.multiplier;
            return {
                currency: replacement.currency,
                amount: newAmount
            };
        };
    }
    return (currencyCode, amount) => ({
        ...(currencyCode ? { currency: Currencies.value(currencyCode) } : {}),
        ...(amount ? { amount } : {})
    });
}
