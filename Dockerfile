FROM node:22.14.0-bullseye as build

# Install dependencies
RUN apt-get -yqq --no-install-recommends install curl unzip git

WORKDIR /app
COPY . /app/

RUN npm install \
    && npm run compile \
    && npm run version

FROM node:22.14.0-alpine as main
EXPOSE 5000

WORKDIR /app
COPY --chown=node:node --from=build /app .

RUN rm -rf coverage .nyc_output && npm cache clean --force && rm .npmrc

USER node
CMD ["node", "--max-http-header-size=81000", "lib/skywind/app"]