{"extends": "tslint:recommended", "rules": {"no-reference": false, "no-string-literal": false, "no-var-requires": false, "no-bitwise": false, "object-literal-sort-keys": false, "interface-name": false, "ordered-imports": false, "object-literal-key-quotes": false, "member-ordering": false, "max-classes-per-file": false, "object-literal-shorthand": false, "arrow-parens": false, "array-type": false, "only-arrow-functions": false, "trailing-comma": false, "space-before-function-paren": ["error", {"asyncArrow": "never"}], "no-trailing-whitespace": false}}