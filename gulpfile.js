const gulp = require('gulp');
const tsc = require('gulp-typescript');
const mocha = require("gulp-mocha");
const sourcemaps = require('gulp-sourcemaps');
const cleanCompiledTypeScript = require('gulp-clean-compiled-typescript');
const del = require('del');
const gulpTslint = require('gulp-tslint');
const file = require('gulp-file');
const fs = require('fs');
const childProcess = require('child_process');

function handleError(err) {
    console.log(err.toString());
    this.emit('end');
}

gulp.task('tslint', async () => {
    return gulp.src(['src/**/*.ts', '!src/**/*.d.ts'])
        .pipe(gulpTslint({
            formatter: "verbose"
        }))
        .pipe(gulpTslint.report());
});

gulp.task('clean-compiled-typescript', async () => {
    return gulp
        .src(['src/**/*.ts'])
        .pipe(cleanCompiledTypeScript());
});

gulp.task('clean', gulp.series('clean-compiled-typescript', () => {
    return del(['./lib']);
}));

gulp.task('full-clean', gulp.series('clean', () => {
    del(['./node_modules']);
}));

gulp.task('compile-server', async () => {
    let errors = false;
    const tsProject = tsc.createProject('tsconfig.json');
    return gulp.src(['src/**/*.ts'])
        .pipe(sourcemaps.init())
        .pipe(tsProject())
        .on('error', function() { errors = true; })
        .on('end', function() { if (errors) process.exit(1);})
        .pipe(sourcemaps.write())
        .pipe(gulp.dest('lib'));
});

gulp.task('compile-server-handle-error', async () => {
    let errors = false;
    const tscProject = tsc.createProject('tsconfig.json');
    return gulp.src(['src/**/*.ts', '!node_modules/**/*.*'])
        .pipe(sourcemaps.init())
        .pipe(tscProject())
        .on('error', handleError)
        .on('end', function() { if (errors) process.exit(1);})
        .pipe(sourcemaps.write())
        .pipe(gulp.dest('lib'));
});

gulp.task('version', async () => {
    const revision = childProcess
        .execSync('git rev-parse HEAD')
        .toString()
        .trim();

    let version = JSON.parse(fs.readFileSync('./package.json')).version;
    let str = version + " " + revision + " " + (new Date()).toString();
    return file('version', str, { src: true })
        .pipe(gulp.dest('lib/skywind/'));
});

gulp.task('test', gulp.series(['compile-server', 'version'], async() => {
    return gulp.src(['lib/test/**/*.spec.js'])
        .pipe(mocha({reporter: 'spec', "exit-flag": "--exit", timeout: 20000}))
        .on('error', function() { process.exit(1); });
}));

gulp.task('default', gulp.series('clean', 'tslint'));
