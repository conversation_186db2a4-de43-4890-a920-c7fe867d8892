version: '2'
services:
  server:
    image: ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}
    build:
      context: .
      dockerfile: Dockerfile
    links:
      - db
    environment:
      PGUSER: postgres
      PGDATABASE: postgres
      EVENT_CONSUMER_PGUSER: postgres
      EVENT_CONSUMER_PGDATABASE: postgres
    command: npm run test
  db:
    image: 'postgres:10'
    command: postgres -c fsync=off -c synchronous_commit=off -c full_page_writes=off
    ports:
      - 5432
    environment:
      POSTGRES_HOST_AUTH_METHOD: trust
