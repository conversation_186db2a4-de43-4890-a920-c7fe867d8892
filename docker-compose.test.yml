version: '3.5'
services:
  db:
    image: postgres
    command: postgres -c fsync=off -c synchronous_commit=off -c full_page_writes=off
    ports:
      - '5432:5432'
    environment:
      - POSTGRES_DB=management
      - POSTGRES_HOST_AUTH_METHOD=trust
      - PGUSER=postgres
      - PGPASSWORD=postgres
  redis:
    image: redis:alpine
    command: redis-server --save "" --appendonly no
    ports:
      - '6379:6379'
